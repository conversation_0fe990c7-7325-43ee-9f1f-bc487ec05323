<?php

namespace App\Controllers;

class HomeMainController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    public function index()
    {
        // Dummy data for UI development - replace with actual model calls later (30 positions)
        $positions = [
            [
                'id' => 1,
                'designation' => 'Senior Software Engineer',
                'org_name' => 'Department of Information Technology',
                'location' => 'Port Moresby',
                'annual_salary' => '85000',
                'classification' => 'Level 8',
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV-2024-001',
                'publish_date_to' => '2024-12-31'
            ],
            [
                'id' => 2,
                'designation' => 'Project Manager',
                'org_name' => 'Department of Works',
                'location' => 'Lae',
                'annual_salary' => '75000',
                'classification' => 'Level 7',
                'exercise_name' => 'Infrastructure Development Exercise',
                'advertisement_no' => 'ADV-2024-002',
                'publish_date_to' => '2024-11-30'
            ],
            [
                'id' => 3,
                'designation' => 'Financial Analyst',
                'org_name' => 'Department of Treasury',
                'location' => 'Port Moresby',
                'annual_salary' => '65000',
                'classification' => 'Level 6',
                'exercise_name' => 'Finance Recruitment 2024',
                'advertisement_no' => 'ADV-2024-003',
                'publish_date_to' => '2024-12-15'
            ],
            [
                'id' => 4,
                'designation' => 'Human Resources Officer',
                'org_name' => 'Department of Personnel Management',
                'location' => 'Port Moresby',
                'annual_salary' => '55000',
                'classification' => 'Level 5',
                'exercise_name' => 'HR Recruitment 2024',
                'advertisement_no' => 'ADV-2024-004',
                'publish_date_to' => '2024-12-20'
            ],
            [
                'id' => 5,
                'designation' => 'Environmental Scientist',
                'org_name' => 'Department of Environment',
                'location' => 'Mount Hagen',
                'annual_salary' => '70000',
                'classification' => 'Level 7',
                'exercise_name' => 'Environment Protection Exercise',
                'advertisement_no' => 'ADV-2024-005',
                'publish_date_to' => '2024-12-25'
            ],
            [
                'id' => 6,
                'designation' => 'Civil Engineer',
                'org_name' => 'Department of Works',
                'location' => 'Madang',
                'annual_salary' => '80000',
                'classification' => 'Level 8',
                'exercise_name' => 'Engineering Recruitment 2024',
                'advertisement_no' => 'ADV-2024-006',
                'publish_date_to' => '2024-12-18'
            ],
            [
                'id' => 7,
                'designation' => 'Health Administrator',
                'org_name' => 'Department of Health',
                'location' => 'Port Moresby',
                'annual_salary' => '60000',
                'classification' => 'Level 6',
                'exercise_name' => 'Health Services Exercise',
                'advertisement_no' => 'ADV-2024-007',
                'publish_date_to' => '2024-12-22'
            ],
            [
                'id' => 8,
                'designation' => 'Education Officer',
                'org_name' => 'Department of Education',
                'location' => 'Vanimo',
                'annual_salary' => '50000',
                'classification' => 'Level 5',
                'exercise_name' => 'Education Recruitment 2024',
                'advertisement_no' => 'ADV-2024-008',
                'publish_date_to' => '2024-12-28'
            ],
            [
                'id' => 9,
                'designation' => 'Agricultural Extension Officer',
                'org_name' => 'Department of Agriculture',
                'location' => 'Wewak',
                'annual_salary' => '45000',
                'classification' => 'Level 4',
                'exercise_name' => 'Agriculture Development Exercise',
                'advertisement_no' => 'ADV-2024-009',
                'publish_date_to' => '2024-12-30'
            ],
            [
                'id' => 10,
                'designation' => 'Police Officer',
                'org_name' => 'Royal Papua New Guinea Constabulary',
                'location' => 'Port Moresby',
                'annual_salary' => '48000',
                'classification' => 'Level 4',
                'exercise_name' => 'Police Recruitment 2024',
                'advertisement_no' => 'ADV-2024-010',
                'publish_date_to' => '2024-12-31'
            ],
            [
                'id' => 11,
                'designation' => 'Customs Officer',
                'org_name' => 'Papua New Guinea Customs Service',
                'location' => 'Lae',
                'annual_salary' => '52000',
                'classification' => 'Level 5',
                'exercise_name' => 'Customs Recruitment 2024',
                'advertisement_no' => 'ADV-2024-011',
                'publish_date_to' => '2024-12-29'
            ],
            [
                'id' => 12,
                'designation' => 'Immigration Officer',
                'org_name' => 'Immigration and Citizenship Service',
                'location' => 'Port Moresby',
                'annual_salary' => '54000',
                'classification' => 'Level 5',
                'exercise_name' => 'Immigration Services Exercise',
                'advertisement_no' => 'ADV-2024-012',
                'publish_date_to' => '2024-12-27'
            ],
            [
                'id' => 13,
                'designation' => 'Tax Officer',
                'org_name' => 'Internal Revenue Commission',
                'location' => 'Port Moresby',
                'annual_salary' => '58000',
                'classification' => 'Level 6',
                'exercise_name' => 'Revenue Collection Exercise',
                'advertisement_no' => 'ADV-2024-013',
                'publish_date_to' => '2024-12-26'
            ],
            [
                'id' => 14,
                'designation' => 'Social Worker',
                'org_name' => 'Department of Community Development',
                'location' => 'Daru',
                'annual_salary' => '46000',
                'classification' => 'Level 4',
                'exercise_name' => 'Community Services Exercise',
                'advertisement_no' => 'ADV-2024-014',
                'publish_date_to' => '2024-12-24'
            ],
            [
                'id' => 15,
                'designation' => 'Forestry Officer',
                'org_name' => 'Papua New Guinea Forest Authority',
                'location' => 'Popondetta',
                'annual_salary' => '49000',
                'classification' => 'Level 4',
                'exercise_name' => 'Forest Management Exercise',
                'advertisement_no' => 'ADV-2024-015',
                'publish_date_to' => '2024-12-23'
            ],
            [
                'id' => 16,
                'designation' => 'Mining Inspector',
                'org_name' => 'Mineral Resources Authority',
                'location' => 'Tabubil',
                'annual_salary' => '72000',
                'classification' => 'Level 7',
                'exercise_name' => 'Mining Regulation Exercise',
                'advertisement_no' => 'ADV-2024-016',
                'publish_date_to' => '2024-12-21'
            ],
            [
                'id' => 17,
                'designation' => 'Fisheries Officer',
                'org_name' => 'National Fisheries Authority',
                'location' => 'Kerema',
                'annual_salary' => '47000',
                'classification' => 'Level 4',
                'exercise_name' => 'Marine Resources Exercise',
                'advertisement_no' => 'ADV-2024-017',
                'publish_date_to' => '2024-12-19'
            ],
            [
                'id' => 18,
                'designation' => 'Tourism Officer',
                'org_name' => 'Papua New Guinea Tourism Promotion Authority',
                'location' => 'Port Moresby',
                'annual_salary' => '53000',
                'classification' => 'Level 5',
                'exercise_name' => 'Tourism Development Exercise',
                'advertisement_no' => 'ADV-2024-018',
                'publish_date_to' => '2024-12-17'
            ],
            [
                'id' => 19,
                'designation' => 'Transport Inspector',
                'org_name' => 'Department of Transport',
                'location' => 'Lae',
                'annual_salary' => '51000',
                'classification' => 'Level 5',
                'exercise_name' => 'Transport Safety Exercise',
                'advertisement_no' => 'ADV-2024-019',
                'publish_date_to' => '2024-12-16'
            ],
            [
                'id' => 20,
                'designation' => 'Communications Officer',
                'org_name' => 'National Information and Communications Technology Authority',
                'location' => 'Port Moresby',
                'annual_salary' => '56000',
                'classification' => 'Level 6',
                'exercise_name' => 'ICT Development Exercise',
                'advertisement_no' => 'ADV-2024-020',
                'publish_date_to' => '2024-12-14'
            ],
            [
                'id' => 21,
                'designation' => 'Energy Officer',
                'org_name' => 'Department of Petroleum and Energy',
                'location' => 'Port Moresby',
                'annual_salary' => '68000',
                'classification' => 'Level 7',
                'exercise_name' => 'Energy Sector Exercise',
                'advertisement_no' => 'ADV-2024-021',
                'publish_date_to' => '2024-12-13'
            ],
            [
                'id' => 22,
                'designation' => 'Water Resources Engineer',
                'org_name' => 'Department of Works',
                'location' => 'Mount Hagen',
                'annual_salary' => '74000',
                'classification' => 'Level 7',
                'exercise_name' => 'Water Infrastructure Exercise',
                'advertisement_no' => 'ADV-2024-022',
                'publish_date_to' => '2024-12-12'
            ],
            [
                'id' => 23,
                'designation' => 'Legal Officer',
                'org_name' => 'Department of Justice and Attorney General',
                'location' => 'Port Moresby',
                'annual_salary' => '78000',
                'classification' => 'Level 8',
                'exercise_name' => 'Legal Services Exercise',
                'advertisement_no' => 'ADV-2024-023',
                'publish_date_to' => '2024-12-11'
            ],
            [
                'id' => 24,
                'designation' => 'Procurement Officer',
                'org_name' => 'Department of Finance',
                'location' => 'Port Moresby',
                'annual_salary' => '59000',
                'classification' => 'Level 6',
                'exercise_name' => 'Financial Management Exercise',
                'advertisement_no' => 'ADV-2024-024',
                'publish_date_to' => '2024-12-10'
            ],
            [
                'id' => 25,
                'designation' => 'Security Officer',
                'org_name' => 'Department of Prime Minister and NEC',
                'location' => 'Port Moresby',
                'annual_salary' => '50000',
                'classification' => 'Level 5',
                'exercise_name' => 'Government Security Exercise',
                'advertisement_no' => 'ADV-2024-025',
                'publish_date_to' => '2024-12-09'
            ],
            [
                'id' => 26,
                'designation' => 'Research Officer',
                'org_name' => 'National Research Institute',
                'location' => 'Port Moresby',
                'annual_salary' => '62000',
                'classification' => 'Level 6',
                'exercise_name' => 'Research Development Exercise',
                'advertisement_no' => 'ADV-2024-026',
                'publish_date_to' => '2024-12-08'
            ],
            [
                'id' => 27,
                'designation' => 'Statistics Officer',
                'org_name' => 'National Statistical Office',
                'location' => 'Port Moresby',
                'annual_salary' => '57000',
                'classification' => 'Level 6',
                'exercise_name' => 'Data Management Exercise',
                'advertisement_no' => 'ADV-2024-027',
                'publish_date_to' => '2024-12-07'
            ],
            [
                'id' => 28,
                'designation' => 'Planning Officer',
                'org_name' => 'Department of National Planning and Monitoring',
                'location' => 'Port Moresby',
                'annual_salary' => '64000',
                'classification' => 'Level 6',
                'exercise_name' => 'National Development Exercise',
                'advertisement_no' => 'ADV-2024-028',
                'publish_date_to' => '2024-12-06'
            ],
            [
                'id' => 29,
                'designation' => 'Audit Officer',
                'org_name' => 'Auditor General Office',
                'location' => 'Port Moresby',
                'annual_salary' => '66000',
                'classification' => 'Level 7',
                'exercise_name' => 'Government Audit Exercise',
                'advertisement_no' => 'ADV-2024-029',
                'publish_date_to' => '2024-12-05'
            ],
            [
                'id' => 30,
                'designation' => 'Public Relations Officer',
                'org_name' => 'Government Information Services',
                'location' => 'Port Moresby',
                'annual_salary' => '55000',
                'classification' => 'Level 5',
                'exercise_name' => 'Communications Exercise',
                'advertisement_no' => 'ADV-2024-030',
                'publish_date_to' => '2024-12-04'
            ]
        ];

        return view('home/home_home', [
            'title' => 'Home',
            'menu' => 'home',
            'navbar_fixed' => true,
            'latest_positions' => $positions
        ]);
    }

    public function about()
    {
        return view('home/home_about', [
            'title' => 'About',
            'menu' => 'about',
            'navbar_fixed' => true
        ]);
    }

    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
