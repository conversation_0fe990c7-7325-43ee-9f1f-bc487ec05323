<?php

namespace App\Controllers;

class ApplicantController extends BaseController
{
    protected $session;
    protected $applicantsModel;
    protected $experiencesModel;
    protected $educationModel;
    protected $educationLevelsModel;
    protected $filesModel;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'application']);
        $this->session = session();
        $this->applicantsModel = new \App\Models\ApplicantsModel();
        $this->experiencesModel = new \App\Models\ApplicantsExperiencesModel();
        $this->educationModel = new \App\Models\ApplicantEducationModel();
        $this->educationLevelsModel = new \App\Models\EducationLevelsModel();
        $this->filesModel = new \App\Models\ApplicantFilesModel();
    }

    public function dashboard()
    {
        // Mock applicant data for UI development
        $applicant = [
            'fname' => 'Demo',
            'lname' => 'Applicant',
            'email' => '<EMAIL>',
            'gender' => 'Male',
            'dobirth' => '1990-01-01',
            'place_of_origin' => 'Port Moresby',
            'contact_details' => '+675 123 4567',
            'location_address' => '123 Main Street, Port Moresby'
        ];

        // Mock application statistics
        $total_applications = 5;
        $pending_applications = 2;
        $shortlisted_applications = 2;
        $rejected_applications = 1;

        // Mock recent applications
        $recent_applications = [
            [
                'id' => 1,
                'position_title' => 'Software Developer',
                'department' => 'Information Technology',
                'created_at' => '2024-01-15',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'position_title' => 'Data Analyst',
                'department' => 'Finance',
                'created_at' => '2024-01-10',
                'status' => 'shortlisted'
            ],
            [
                'id' => 3,
                'position_title' => 'Project Manager',
                'department' => 'Operations',
                'created_at' => '2024-01-05',
                'status' => 'rejected'
            ]
        ];

        // Mock latest job openings
        $latest_jobs = [
            [
                'id' => 1,
                'title' => 'Senior Developer',
                'department' => 'IT Department',
                'location' => 'Port Moresby',
                'posted_date' => '2024-01-20'
            ],
            [
                'id' => 2,
                'title' => 'Marketing Specialist',
                'department' => 'Marketing',
                'location' => 'Lae',
                'posted_date' => '2024-01-18'
            ],
            [
                'id' => 3,
                'title' => 'HR Officer',
                'department' => 'Human Resources',
                'location' => 'Mount Hagen',
                'posted_date' => '2024-01-16'
            ]
        ];

        return view('applicant/applicant_dashboard', [
            'title' => 'Dashboard',
            'menu' => 'dashboard',
            'applicant' => $applicant,
            'total_applications' => $total_applications,
            'pending_applications' => $pending_applications,
            'shortlisted_applications' => $shortlisted_applications,
            'rejected_applications' => $rejected_applications,
            'recent_applications' => $recent_applications,
            'latest_jobs' => $latest_jobs
        ]);
    }

    public function profile()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to access your profile');
        }

        // Get applicant data from database
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('applicant/login')->with('error', 'Applicant not found');
        }

        // Get work experiences from database
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                             ->orderBy('date_from', 'DESC')
                                             ->findAll();

        // Get education records from database
        $education = $this->educationModel->where('applicant_id', $applicant_id)
                                         ->orderBy('date_from', 'DESC')
                                         ->findAll();

        // Get education levels from database
        $education_data = $this->educationLevelsModel->getActiveEducationLevels();

        // Create education levels array for backward compatibility
        $education_levels = [];
        foreach ($education_data as $level) {
            $education_levels[$level['id']] = $level['name'];
        }

        // Get applicant files from database
        $files = $this->filesModel->where('applicant_id', $applicant_id)
                                 ->orderBy('created_at', 'DESC')
                                 ->findAll();

        return view('applicant/applicant_profile', [
            'title' => 'Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_levels' => $education_levels,
            'education_data' => $education_data,
            'files' => $files
        ]);
    }

    // Personal Information Update
    public function updatePersonal()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'gender' => 'required|in_list[Male,Female]',
            'dobirth' => 'required|valid_date',
            'contact_details' => 'required|min_length[5]',
            'location_address' => 'permit_empty|max_length[500]',
            'place_of_origin' => 'permit_empty|max_length[255]',
            'citizenship' => 'permit_empty|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'contact_details' => $this->request->getPost('contact_details'),
            'location_address' => $this->request->getPost('location_address'),
            'place_of_origin' => $this->request->getPost('place_of_origin'),
            'citizenship' => $this->request->getPost('citizenship'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);

            // Update session name
            session()->set('applicant_name', trim($data['first_name'] . ' ' . $data['last_name']));

            return redirect()->to('applicant/profile#personal')->with('success', 'Personal information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating personal information: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error updating personal information. Please try again.');
        }
    }

    // Documents Update
    public function updateDocuments()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'id_numbers' => $this->request->getPost('id_numbers'),
            'offence_convicted' => $this->request->getPost('offence_convicted'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#documents')->with('success', 'Documents updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating documents: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating documents. Please try again.');
        }
    }

    // Employment Update
    public function updateEmployment()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'current_salary' => $this->request->getPost('current_salary'),
            'how_did_you_hear_about_us' => $this->request->getPost('how_did_you_hear_about_us'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#employment')->with('success', 'Employment information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating employment: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating employment information. Please try again.');
        }
    }

    // Add Experience
    public function addExperience()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'employer' => 'required|min_length[2]|max_length[255]',
            'position' => 'required|min_length[2]|max_length[255]',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'employer_contacts_address' => 'permit_empty|max_length[500]',
            'work_description' => 'permit_empty|max_length[1000]',
            'achievements' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'applicant_id' => $applicant_id,
            'employer' => $this->request->getPost('employer'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'work_description' => $this->request->getPost('work_description'),
            'achievements' => $this->request->getPost('achievements'),
            'created_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->insert($data);
            return redirect()->to('applicant/profile#experiences')->with('success', 'Work experience added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error adding experience: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error adding work experience. Please try again.');
        }
    }

    // Update Experience
    public function updateExperience()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $experience_id = $this->request->getPost('id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return redirect()->back()->with('error', 'Experience not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'employer' => 'required|min_length[2]|max_length[255]',
            'position' => 'required|min_length[2]|max_length[255]',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'employer_contacts_address' => 'permit_empty|max_length[500]',
            'work_description' => 'permit_empty|max_length[1000]',
            'achievements' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'employer' => $this->request->getPost('employer'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'work_description' => $this->request->getPost('work_description'),
            'achievements' => $this->request->getPost('achievements'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->update($experience_id, $data);
            return redirect()->to('applicant/profile#experiences')->with('success', 'Work experience updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating experience: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating work experience. Please try again.');
        }
    }

    // Delete Experience
    public function deleteExperience($experience_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return redirect()->back()->with('error', 'Experience not found or access denied');
        }

        try {
            $this->experiencesModel->delete($experience_id);
            return redirect()->to('applicant/profile#experiences')->with('success', 'Work experience deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting experience: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting work experience. Please try again.');
        }
    }

    // Add Education
    public function addEducation()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'institution' => 'required|min_length[2]|max_length[255]',
            'course' => 'required|min_length[2]|max_length[255]',
            'education_level' => 'required|numeric',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'units' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'applicant_id' => $applicant_id,
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'education_level' => $this->request->getPost('education_level'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'units' => $this->request->getPost('units'),
            'created_by' => $applicant_id
        ];

        try {
            $this->educationModel->insert($data);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error adding education: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error adding education record. Please try again.');
        }
    }

    // Update Education
    public function updateEducation()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $education_id = $this->request->getPost('id');

        // Verify ownership
        $education = $this->educationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return redirect()->back()->with('error', 'Education record not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'institution' => 'required|min_length[2]|max_length[255]',
            'course' => 'required|min_length[2]|max_length[255]',
            'education_level' => 'required|numeric',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'units' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'education_level' => $this->request->getPost('education_level'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'units' => $this->request->getPost('units'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->educationModel->update($education_id, $data);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating education: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating education record. Please try again.');
        }
    }

    // Delete Education
    public function deleteEducation($education_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $education = $this->educationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return redirect()->back()->with('error', 'Education record not found or access denied');
        }

        try {
            $this->educationModel->delete($education_id);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting education: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting education record. Please try again.');
        }
    }

    // Update Family Information
    public function updateFamily()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Process children data
        $children_data = $this->request->getPost('children');
        $children_json = null;

        if ($children_data && is_array($children_data)) {
            // Filter out empty children entries
            $filtered_children = array_filter($children_data, function($child) {
                return !empty($child['name']) || !empty($child['dob']) || !empty($child['gender']);
            });

            if (!empty($filtered_children)) {
                $children_json = json_encode(array_values($filtered_children));
            }
        }

        $data = [
            'marital_status' => $this->request->getPost('marital_status'),
            'date_of_marriage' => $this->request->getPost('date_of_marriage') ?: null,
            'spouse_employer' => $this->request->getPost('spouse_employer'),
            'children' => $children_json,
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        // Remove fields that don't exist in the model's allowedFields
        $allowedFields = $this->applicantsModel->getAllowedFields();
        $data = array_intersect_key($data, array_flip($allowedFields));

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#family')->with('success', 'Family information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating family information: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating family information. Please try again.');
        }
    }

    // Update Additional Information
    public function updateAdditional()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#additional')->with('success', 'Additional information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating additional information: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating additional information. Please try again.');
        }
    }

    // Upload File
    public function uploadFile()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]',
            'file' => 'uploaded[file]|max_size[file,5120]|ext_in[file,pdf,doc,docx,jpg,jpeg,png]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $file = $this->request->getFile('file');

        if ($file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if ($file->move($uploadPath, $newName)) {
                $data = [
                    'applicant_id' => $applicant_id,
                    'file_title' => $this->request->getPost('file_title'),
                    'file_description' => $this->request->getPost('file_description'),
                    'file_path' => 'public/uploads/applicants/' . $applicant_id . '/' . $newName,
                    'created_by' => $applicant_id
                ];

                try {
                    $this->filesModel->insert($data);
                    return redirect()->to('applicant/profile#files')->with('success', 'File uploaded successfully');
                } catch (\Exception $e) {
                    log_message('error', 'Error saving file record: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error saving file record. Please try again.');
                }
            } else {
                return redirect()->back()->with('error', 'Error uploading file. Please try again.');
            }
        } else {
            return redirect()->back()->with('error', 'Invalid file or file upload error.');
        }
    }

    // Update File
    public function updateFile()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $file_id = $this->request->getPost('file_id');

        // Verify ownership
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'File not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'file_title' => $this->request->getPost('file_title'),
            'file_description' => $this->request->getPost('file_description'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->filesModel->update($file_id, $data);
            return redirect()->to('applicant/profile#files')->with('success', 'File information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating file: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating file information. Please try again.');
        }
    }

    // Delete File
    public function deleteFile($file_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'File not found or access denied');
        }

        try {
            // Delete physical file (remove 'public/' prefix for file system path)
            $physicalPath = str_replace('public/', FCPATH, $file['file_path']);
            if (file_exists($physicalPath)) {
                unlink($physicalPath);
            }

            // Delete database record
            $this->filesModel->delete($file_id);
            return redirect()->to('applicant/profile#files')->with('success', 'File deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting file: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting file. Please try again.');
        }
    }

    // Change Password
    public function changePassword()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'current_password' => 'required',
            'new_password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[new_password]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Get current applicant data
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->back()->with('error', 'Applicant not found');
        }

        // Verify current password
        if (!password_verify($this->request->getPost('current_password'), $applicant['password'])) {
            return redirect()->back()->with('error', 'Current password is incorrect');
        }

        $data = [
            'password' => $this->request->getPost('new_password'), // Will be hashed by model callback
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#security')->with('success', 'Password changed successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error changing password: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error changing password. Please try again.');
        }
    }

    // Upload Photo
    public function uploadPhoto()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'id_photo' => 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->with('errors', $validation->getErrors());
        }

        $file = $this->request->getFile('id_photo');

        if ($file->isValid() && !$file->hasMoved()) {
            $newName = 'photo_' . $applicant_id . '_' . time() . '.' . $file->getExtension();
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if ($file->move($uploadPath, $newName)) {
                $data = [
                    'id_photo_path' => 'public/uploads/applicants/' . $applicant_id . '/' . $newName,
                    'updated_by' => $applicant_id
                ];

                try {
                    $this->applicantsModel->update($applicant_id, $data);
                    return redirect()->to('applicant/profile')->with('success', 'Profile photo updated successfully');
                } catch (\Exception $e) {
                    log_message('error', 'Error updating photo: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error updating profile photo. Please try again.');
                }
            } else {
                return redirect()->back()->with('error', 'Error uploading photo. Please try again.');
            }
        } else {
            return redirect()->back()->with('error', 'Invalid photo file or upload error.');
        }
    }

    // Note: serveFile method removed since files are now stored in public directory
    // and can be accessed directly via base_url()
}