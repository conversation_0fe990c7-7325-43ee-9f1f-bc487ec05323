<?php

namespace App\Models;

use CodeIgniter\Model;

class ApplicantsExperiencesModel extends Model
{
    protected $table = 'applicants_experiences';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'applicant_id',
        'employer',
        'employer_contacts_address',
        'position',
        'date_from',
        'date_to',
        'achievements',
        'work_description',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
}