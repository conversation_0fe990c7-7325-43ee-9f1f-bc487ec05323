<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * EducationLevelsModel
 *
 * Model for the education_levels table
 */
class EducationLevelsModel extends Model
{
    protected $table = 'education_levels';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    protected $allowedFields = [
        'name',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|max_length[150]|is_unique[education_levels.name,id,{id}]'
    ];

    protected $validationMessages = [
        'name' => [
            'required'    => 'Education level name is required',
            'max_length'  => 'Education level name cannot exceed 150 characters',
            'is_unique'   => 'Education level name already exists'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get all active education levels
     *
     * @return array
     */
    public function getActiveEducationLevels()
    {
        return $this->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Get education level by name
     *
     * @param string $name
     * @return array|null
     */
    public function getEducationLevelByName($name)
    {
        return $this->where('name', $name)->first();
    }

    /**
     * Search education levels by name
     *
     * @param string $search
     * @return array
     */
    public function searchEducationLevels($search)
    {
        return $this->like('name', $search)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get education level statistics
     *
     * @return array
     */
    public function getEducationLevelStatistics()
    {
        $stats = [];

        // Total education levels
        $stats['total'] = $this->countAllResults(false);

        // Education levels with remarks
        $stats['with_remarks'] = $this->where('remarks IS NOT NULL')
                                      ->where('remarks !=', '')
                                      ->countAllResults(false);

        return $stats;
    }
}