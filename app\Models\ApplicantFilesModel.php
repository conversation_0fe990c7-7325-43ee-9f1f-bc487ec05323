<?php

namespace App\Models;

use CodeIgniter\Model;

class ApplicantFilesModel extends Model
{
    protected $table = 'applicant_files';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'applicant_id',
        'file_title',
        'file_description',
        'file_path',
        'file_extracted_texts',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
}